<component name="libraryTable">
  <library name="Maven: org.checkerframework:checker-qual:3.5.0">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>