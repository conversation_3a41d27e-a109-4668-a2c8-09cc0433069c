# 生产环境配置
server:
  port: 54321
  servlet:
    context-path: /multi/source/api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 20000
    max-connections: 8192
    accept-count: 100
    max-http-form-post-size: 2MB

# 多MySQL数据源配置 - 生产环境
datasources:
  # 船管系统数据库 - 生产环境
  crew:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ******************************************************************************************************************************
    username: root
    password: winsea@2020
    type: mysql

  # 航次动态管理数据库 - 生产环境
  voyage:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **********************************************************************************************************************************
    username: root
    password: hzx_root
    type: mysql

  # 货物管理数据库 - 生产环境
  cargo:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: *********************************************************************************************************************************
    username: cargo_user
    password: cargo_password
    type: mysql

  # 财务管理数据库 - 生产环境
  finance:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ***********************************************************************************************************************************
    username: finance_user
    password: finance_password
    type: mysql

# HikariCP连接池配置 - 生产环境（较大的连接池）
hikari:
  maximum-pool-size: 50
  minimum-idle: 10
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
  leak-detection-threshold: 120000
  connection-test-query: SELECT 1

# 日志配置 - 生产环境（精简日志）
logging:
  level:
    root: WARN
    com.example.multidatasource: INFO
    org.springframework.jdbc: WARN
    org.apache.ibatis: WARN
    org.mybatis: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/multi-datasource/multi-datasource-prod.log
    max-size: 500MB
    max-history: 90
  logback:
    rollingpolicy:
      max-file-size: 500MB
      total-size-cap: 10GB

# 生产环境安全配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要端点
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      sla:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms

# 生产环境数据库配置
spring:
  sql:
    init:
      mode: never  # 生产环境不执行初始化脚本
  jpa:
    show-sql: false  # 生产环境不显示SQL

# JWT配置 - 生产环境
jwt:
  secret: myProductionSecretKey123456789012345678901234567890  # JWT密钥
  expiration: 604800  # 令牌过期时间（秒）
  issuer: multi-datasource-api-prod

# swagger地址信息
swagger:
  server:
    url: http://114.55.171.229:54321/multi/source/api