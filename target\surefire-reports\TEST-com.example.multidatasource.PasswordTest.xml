<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.multidatasource.PasswordTest" time="1.53" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\augmentSpace\target\test-classes;D:\augmentSpace\target\classes;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;F:\learing\apache-maven-3.6.1\mavenRepository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.2\mybatis-spring-boot-starter-2.3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.2\mybatis-spring-boot-autoconfigure-2.3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\mybatis\3.5.14\mybatis-3.5.14.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-boot-starter\3.5.3.1\mybatis-plus-boot-starter-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus\3.5.3.1\mybatis-plus-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-extension\3.5.3.1\mybatis-plus-extension-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-core\3.5.3.1\mybatis-plus-core-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-annotation\3.5.3.1\mybatis-plus-annotation-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-generator\3.5.3.1\mybatis-plus-generator-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-security\2.7.18\spring-boot-starter-security-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\oracle\database\jdbc\ojdbc8\21.5.0.0\ojdbc8-21.5.0.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\microsoft\sqlserver\mssql-jdbc\10.2.3.jre8\mssql-jdbc-10.2.3.jre8.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\ow2\asm\asm\9.3\asm-9.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2041029266731608527\surefirebooter1242482173395690671.jar C:\Users\<USER>\AppData\Local\Temp\surefire2041029266731608527 2025-07-25T10-40-45_790-jvmRun1 surefire7385766477976125564tmp surefire_04811176471700550734tmp"/>
    <property name="surefire.test.class.path" value="D:\augmentSpace\target\test-classes;D:\augmentSpace\target\classes;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;F:\learing\apache-maven-3.6.1\mavenRepository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.2\mybatis-spring-boot-starter-2.3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.2\mybatis-spring-boot-autoconfigure-2.3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\mybatis\3.5.14\mybatis-3.5.14.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-boot-starter\3.5.3.1\mybatis-plus-boot-starter-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus\3.5.3.1\mybatis-plus-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-extension\3.5.3.1\mybatis-plus-extension-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-core\3.5.3.1\mybatis-plus-core-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-annotation\3.5.3.1\mybatis-plus-annotation-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\baomidou\mybatis-plus-generator\3.5.3.1\mybatis-plus-generator-3.5.3.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-security\2.7.18\spring-boot-starter-security-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\oracle\database\jdbc\ojdbc8\21.5.0.0\ojdbc8-21.5.0.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\microsoft\sqlserver\mssql-jdbc\10.2.3.jre8\mssql-jdbc-10.2.3.jre8.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\ow2\asm\asm\9.3\asm-9.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;F:\learing\apache-maven-3.6.1\mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;F:\learing\apache-maven-3.6.1\mavenRepository\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;F:\learing\apache-maven-3.6.1\mavenRepository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk1.8.0_202\jre"/>
    <property name="basedir" value="D:\augmentSpace"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2041029266731608527\surefirebooter1242482173395690671.jar"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_202\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_202-b08"/>
    <property name="user.name" value="chiqiyun"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="F:\learing\apache-maven-3.6.1\mavenRepository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2022.3.3"/>
    <property name="java.version" value="1.8.0_202"/>
    <property name="user.dir" value="D:\augmentSpace"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Java\jdk1.8.0_202\bin;G:\XshellAndXftp\Xftp\;G:\XshellAndXftp\Xshell\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;G:\SVN\bin;G:\git\setups\Git\cmd;F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\bin;C:\Program Files\Java\jdk1.8.0_202\jre\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;G:\IntelliJ IDEA 2022.3.3\bin;;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;G:\VSCode\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.202-b08"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testPasswordMatching" classname="com.example.multidatasource.PasswordTest" time="1.511"/>
</testsuite>