package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.mapper.SeafarerScheduleMapper;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.crew.service.SeafarerScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 船员管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerScheduleServiceImpl implements SeafarerScheduleService {

    @Autowired
    private SeafarerScheduleMapper seafarerScheduleMapper;

    @Autowired
    private SeafarerMatchingService seafarerMatchingService;


    @Override
    public PageResult<CrewInfo> queryCrewsPage(Integer page, Integer size, String seafarerId, String seafarerName, String applyDutyId) {
        int offset = (page - 1) * size;
        List<CrewInfo> records = seafarerScheduleMapper.selectCrewList(offset, size, seafarerId, seafarerName, applyDutyId);
        int total = seafarerScheduleMapper.countCrew(seafarerId, seafarerName, applyDutyId);

        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public List<Map<String, Object>> getSeafarerCertificateInfo(String seafarerId) {
        log.info("查询船员证书到期列表, seafarerId: {}", seafarerId);
        try {
            List<Map<String, Object>> result = seafarerScheduleMapper.getSeafarerCertificateInfo(seafarerId);
            log.info("成功查询到{}条证书记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询船员证书信息失败, seafarerId: {}", seafarerId, e);
            throw new RuntimeException("查询船员证书信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSeafarerQualificationInfo(String seafarerId, String applyDutyId) {
        log.info("查询船员服务资历, seafarerId: {}, applyDutyId: {}", seafarerId, applyDutyId);
        try {
            List<Map<String, Object>> result = seafarerScheduleMapper.getSeafarerQualificationInfo(seafarerId, applyDutyId);
            log.info("成功查询到{}条服务资历记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询船员服务资历失败, seafarerId: {}, applyDutyId: {}", seafarerId, applyDutyId, e);
            throw new RuntimeException("查询船员服务资历失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 匹配所有候选人");
        return seafarerMatchingService.matchSeafarerForShiftChange(request);
    }

    @Override
    public List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 单个最优匹配");
        return seafarerMatchingService.matchSingleSeafarerForShiftChange(request);
    }



}
