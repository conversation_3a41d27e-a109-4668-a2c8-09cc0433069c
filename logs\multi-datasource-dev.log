2025-07-21 09:02:56 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 21432 (D:\augmentSpace\target\classes started by <PERSON><PERSON><PERSON><PERSON> in D:\augmentSpace)
2025-07-21 09:02:56 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-21 09:02:56 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-21 09:02:57 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-21 09:02:57 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-21 09:02:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-21 09:02:58 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-21 09:02:58 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 09:02:59 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-21 09:02:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2758 ms
2025-07-21 09:02:59 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-21 09:02:59 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-21 09:03:00 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-21 09:03:00 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-21 09:03:00 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-21 09:03:00 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-21 09:03:00 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0d36d21f-ca3c-4e93-bb25-f0c7c9b20c66

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-21 09:03:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5e67a490, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2dac2e1b, org.springframework.security.web.context.SecurityContextPersistenceFilter@759a678a, org.springframework.security.web.header.HeaderWriterFilter@28989415, org.springframework.web.filter.CorsFilter@38c761e9, org.springframework.security.web.authentication.logout.LogoutFilter@113dcaf8, com.example.multidatasource.config.JwtAuthenticationFilter@7fda2001, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@554566a8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b832551, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cee2e10, org.springframework.security.web.session.SessionManagementFilter@a21c74, org.springframework.security.web.access.ExceptionTranslationFilter@6d969330, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56c0a61e]
2025-07-21 09:03:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-21 09:03:01 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 6.685 seconds (JVM running for 8.94)
2025-07-21 09:03:07 [http-nio-54321-exec-2] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 09:03:07 [http-nio-54321-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-21 09:03:07 [http-nio-54321-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-21 09:03:09 [http-nio-54321-exec-8] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1268 ms
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:03:27 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fb7401f] was not registered for synchronization because synchronization is not active
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:03:27 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:03:27 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-21 09:03:28 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@808778116 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fb7401f]
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:03:28 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:03:28 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b9aac8b] was not registered for synchronization because synchronization is not active
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1072248698 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-21T09:03:28.297(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b9aac8b]
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:03:28 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:03:29 [http-nio-54321-exec-10] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:04:04 [http-nio-54321-exec-2] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-21 09:04:04 [http-nio-54321-exec-2] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b46fd80] was not registered for synchronization because synchronization is not active
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-21 09:04:04 [http-nio-54321-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-21 09:04:04 [http-nio-54321-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1964693562 wrapping com.mysql.cj.jdbc.ConnectionImpl@20ab1d72] will not be managed by Spring
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, latest.mmsi_code, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_consum, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b46fd80]
2025-07-21 09:04:04 [http-nio-54321-exec-2] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:04:04 [http-nio-54321-exec-2] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:19:06 [http-nio-54321-exec-3] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@727ee016] was not registered for synchronization because synchronization is not active
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@891765267 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@727ee016]
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:19:06 [http-nio-54321-exec-3] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:19:06 [http-nio-54321-exec-3] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@379321f8] was not registered for synchronization because synchronization is not active
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@950786755 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-21T09:19:06.202(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@379321f8]
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:19:06 [http-nio-54321-exec-3] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:26:58 [http-nio-54321-exec-6] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f4714c] was not registered for synchronization because synchronization is not active
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1830401370 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f4714c]
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:26:58 [http-nio-54321-exec-6] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-21 09:26:58 [http-nio-54321-exec-6] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c22892f] was not registered for synchronization because synchronization is not active
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1018252436 wrapping com.mysql.cj.jdbc.ConnectionImpl@5db709c4] will not be managed by Spring
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-21T09:26:58.408(LocalDateTime), 127.0.0.1(String), 3(Long)
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c22892f]
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:26:58 [http-nio-54321-exec-6] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 127.0.0.1
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:27:40 [http-nio-54321-exec-8] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-21 09:27:40 [http-nio-54321-exec-8] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@987f954] was not registered for synchronization because synchronization is not active
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1090197345 wrapping com.mysql.cj.jdbc.ConnectionImpl@20ab1d72] will not be managed by Spring
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, latest.mmsi_code, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_consum, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-21 09:27:40 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-21 09:27:41 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-21 09:27:41 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@987f954]
2025-07-21 09:27:41 [http-nio-54321-exec-8] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-21 09:27:41 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:27:41 [http-nio-54321-exec-8] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 09:31:47 [http-nio-54321-exec-2] WARN  c.e.m.config.SecurityConfig - Unauthorized access attempt: Full authentication is required to access this resource
2025-07-21 09:32:18 [http-nio-54321-exec-3] WARN  c.e.m.config.SecurityConfig - Unauthorized access attempt: Full authentication is required to access this resource
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:37:19 [http-nio-54321-exec-6] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-21 09:37:19 [http-nio-54321-exec-6] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1242ade9] was not registered for synchronization because synchronization is not active
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@800684337 wrapping com.mysql.cj.jdbc.ConnectionImpl@27b005be] will not be managed by Spring
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, latest.mmsi_code, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_consum, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1242ade9]
2025-07-21 09:37:19 [http-nio-54321-exec-6] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-21 09:37:19 [http-nio-54321-exec-6] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-21 10:08:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-21 10:08:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-21 10:08:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-07-21 10:08:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
