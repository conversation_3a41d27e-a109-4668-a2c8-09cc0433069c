<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="multi-datasource-api" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="augmentSpace" target="1.8" />
      <module name="multi-datasource-api" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="augmentSpace" options="-parameters" />
      <module name="multi-datasource-api" options="-parameters" />
    </option>
  </component>
</project>