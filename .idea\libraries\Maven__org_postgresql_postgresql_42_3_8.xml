<component name="libraryTable">
  <library name="Maven: org.postgresql:postgresql:42.3.8">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/postgresql/postgresql/42.3.8/postgresql-42.3.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/postgresql/postgresql/42.3.8/postgresql-42.3.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/postgresql/postgresql/42.3.8/postgresql-42.3.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>